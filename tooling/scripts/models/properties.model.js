import mongoose from 'mongoose';

const propertySchema = mongoose.Schema(
  {
    id: String,
    recordType: {
        type: String,
        default: "property"
    },
    lastViewedAt: {
        type: Date,
        default: Date.now
    },
    lastViewedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    name: String,
    isDeleted:Boolean,
    deletedDate:Date,
    address: { 
        default: "", 
        type: String 
    },
    city: { 
        default: "", 
        type: String 
    },
    status: { 
        default: "None", 
        type: String 
    },
    state: { 
        default: "", 
        type: String 
    },
    zipCode: Number,
    summary: String,
    ownerName:String,
    county: String,
    yearBuilt: String,
    stories: String,
    lastSold: String,
    submarket: String,
    subType: String,
    zillowListingID: String,
    coverImage: String,
    creator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    deletedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    tags: { 
        type: Array, 
        default: [] 
    },
    phone:  { 
        type: Array, 
        default: [] 
    },
    email:  { 
        type: Array, 
        default: [] 
    },
    propertyImages: [{ 
        url: String, 
        key: String 
    }],
    tasks: [{ 
        type: mongoose.Schema.Types.ObjectId, 
        ref: "Task" 
    }],
    team: { 
        type: mongoose.Schema.Types.ObjectId, 
        ref: "Team" 
    },
    docs: [{
        url: { type: String },
        key: { type: String },
        type: { type: String },
        icon: { type: String },
        name: { type: String },
    }],
    coordinates: {
        lat: { 
            type: Number, 
            default: 0 
        },
        lng: { 
            type: Number, 
            default: 0 
        },
    },
    owner: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ContactDetails",
    },
    propertyData: {
        lastSold: {
            type: String,
            default: ""
        },
        soldPrice: {
            type: Number,
            default: ""
        },
        yearBuilt: {
            type: Number,
            default: 0
        },
        yearRenov: {
            type: Number,
        },
        address: {
            type: String,
            default: ""
        },
        SQFT: {
            type: Number,
        },
        usableSqft: {
            type: Number,
        },
        vacantSqft: {
            type: Number,
        },
        floors: {
            type: Number,
        },
        usablePercentage: {
            type: Number,
        },
        vacantPercentage: {
            type: Number,
        },
        acres: {
            type: Number,
            default: ""
        },
        condition: {
            type: String,
        },
        lot: {
            type: Number,
            //default: ""
        },
        lotType: {
            type: String,
        },
        parking: {
            type: String,
            default: ""
        },
        parcelNo: {
            type: String,
            default: ""
        },
        foundation: {
            type: String,
            default: ""
        },
        construction: {
            type: String,
            default: ""
        },
        propertyValue: {
            type: String,
            default: ""
        },
        bathroom: {
            type: Number,
            default: ""
        },
        bedroom: {
            type: Number,
            default: ""
        },
        family: {
            type: String,
            default: ""
        },
        stories: {
            type: String,
            default: ""
        },
        units: {
            type: Number,
        },
        occupancy: {
            type: String,
            default: ""
        },
        buildingStatus: {
            type: String,
            default: ""
        },
        buildingType: {
            type: String,
            default: ""
        },
        style: {
            type: String,
            default: ""
        },
        forsale: {
            sale: {
                type: Boolean,
                default: false
            },
            price: {
                type: String,
                default: "",
            },
            escrow: {
                type: Boolean,
                default: false,
            },
            status: {
                type: String,
                default: ""
            },
            listingPrice: {
                type: String,
                default: ""
            },
            listingDate: {
                type: String,
                default: ""
            },
            listingAgent: {
                type: String,
                default: ""
            },
            listingAgentPhone: {
                type: String,
                default: ""
            },
            listingAgentEmail: {
                type: String,
                default: ""
            },
            listingAgentURL: {
                type: String,
                default: ""
            },
            cap: {
                type: Number,
                default: 0
            },
            grm: {
                type: Number,
                default: 0
            },
            dollarPerSqFt: {
                type: Number,
                default: 0
            },
            dollarPerUnit: {
                type: Number,
                default: ""
            },
        },
        mostrecentsale: {
            price: {
                type: String,
                default: ""
            },
            date: {
                type: String,
                default: ""
            },
            cap: {
                type: Number,
                default: 0
            },
            grm: {
                type: Number,
                default: 0
            },
            dollarPerSqFt: {
                type: Number,
                default: 0
            },
            dollarPerUnit: {
                type: Number,
                default: 0
            },
            listingAgent: {
                type: String,
                default: ""
            },
            sellingAgent: {
                type: String,
                default: ""
            },
            saleSeller: {
                type: String,
                default: ""
            },
            saleBuyer: {
                type: String,
                default: ""
            },
            lender: {
                type: String,
                default: ""
            },
            referral: {
                type: String,
                default: ""
            },
            daysOnMarket: {
                type: Number,
                default: 0
            },
        },
        unitMixes: [{
            unitType: String,
            beds:String,
            bath:String,
            noUnits:String,
            // noUnits: String,
            unitMinSqft: String,
            unitMaxSqft: String,
            unitMinPrice: String,
            unitMaxPrice: String,
            unitMinRent: String,
            unitMaxRent: String,
        }],
        meterType: {
            type: String,
            default: ""
        },
        zoning: {
            type: String,
            default: ""
        },
        class: {
            type: String,
            default: ""
        },
        structures: {
            type: String,
            default: ""
        },
        landType: {
            type: String,
        },
        landValue: {
            type: Number,
            default: ""
        },
        bldgValue: {
            type: Number,
            default: ""
        },
        tract: {
            type: String,
        },
        improvements: {
            type: String,
        },
    },
    linkedContacts: {
      type: Number,
      default: 0,
    },
    type: {
      type: String,
      default: "",
    },
  },
  { timestamps: true }
);

const PropertyDetails = mongoose.model('PropertyDetails', propertySchema);

export default PropertyDetails;
