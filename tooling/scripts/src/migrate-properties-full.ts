import { PrismaClient } from '@prisma/client';
import mongoose from 'mongoose';
import { ObjectId } from 'mongodb';
import PropertyDetails from '../models/properties.model.js';

const prisma = new PrismaClient();

// Google Geocoding API configuration
const GOOGLE_API_KEY = "AIzaSyC-YxUvCB8t09dkGuPz1aleKKh7_9KYsrQ";

// Progress tracking
let processed = 0;
let successful = 0;
let skipped = 0;
let failed = 0;
let totalProperties = 0;
let startTime = Date.now();
let geocodedCount = 0;

// Cache for organizations and users
const organizationCache = new Map<string, string>();
const userCache = new Map<string, string>();
const companyCache = new Map<string, string>();

// Rate limiting for geocoding
let lastGeocodingCall = 0;
const GEOCODING_DELAY = 100; // 100ms delay between calls (10 requests/second)

// Function to geocode an address using Google Maps API
async function geocodeAddress(address: any): Promise<{ lat: number; lng: number } | null> {
  if (!GOOGLE_API_KEY) {
    console.log('⚠️ Google Maps API key not found. Skipping geocoding.');
    return null;
  }

  // Build address string - handle both full address and county-only
  let addressParts: string[] = [];
  
  if (address.street || address.city || address.state || address.zip) {
    // Full address
    addressParts = [
      address.street,
      address.city,
      address.state,
      address.zip,
      address.country
    ].filter(part => part && part.trim());
  } else if (address.county) {
    // County-only fallback
    addressParts = [
      address.county,
      address.country || 'US'
    ].filter(part => part && part.trim());
  }

  if (addressParts.length === 0) {
    return null;
  }

  const addressString = addressParts.join(', ');
  
  try {
    // Rate limiting
    const now = Date.now();
    const timeSinceLastCall = now - lastGeocodingCall;
    if (timeSinceLastCall < GEOCODING_DELAY) {
      await new Promise(resolve => setTimeout(resolve, GEOCODING_DELAY - timeSinceLastCall));
    }
    lastGeocodingCall = Date.now();

    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(addressString)}&key=${GOOGLE_API_KEY}`;
    const response = await fetch(url);
    const data = await response.json();

    if (data.status === 'OK' && data.results && data.results.length > 0) {
      const location = data.results[0].geometry.location;
      geocodedCount++;
      
      if (geocodedCount % 50 === 0) {
        console.log(`🗺️ Geocoded ${geocodedCount} addresses`);
      }
      
      return {
        lat: location.lat,
        lng: location.lng
      };
    } else {
      // console.log(`⚠️ Geocoding failed for: ${addressString} - ${data.status}`);
      return null;
    }
  } catch (error) {
    console.error(`❌ Geocoding error for ${addressString}:`, error);
    return null;
  }
}

// Property data transformation functions
function transformPropertyType(type?: string): string | undefined {
  if (!type) return undefined;
  
  // Map PropBear types to standardized types
  const typeMap: { [key: string]: string } = {
    'STUCCO': 'residential',
    'WOOD': 'residential', 
    'CONCRETE': 'commercial',
    'BRICK': 'residential',
    'STEEL': 'commercial',
    'MOBILE': 'residential'
  };
  
  return typeMap[type.toUpperCase()] || 'residential';
}

function transformAddress(property: any) {
  const address = {
    street: property.address || '',
    city: property.city || '',
    state: property.state || '',
    zip: property.zipCode?.toString() || '',
    county: property.county || '',
    country: 'US'
  };
  
  return address;
}

function transformLocation(coordinates?: { lat: number; lng: number }) {
  if (!coordinates) {
    return null;
  }
  
  // Convert to numbers and validate
  const lat = parseFloat(String(coordinates.lat));
  const lng = parseFloat(String(coordinates.lng));
  
  // Check if coordinates are valid numbers and not zero/empty
  if (isNaN(lat) || isNaN(lng) || (lat === 0 && lng === 0)) {
    return null;
  }
  
  // Check if coordinates are within valid ranges
  if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
    return null;
  }
  
  return {
    type: 'Point' as const,
    coordinates: [lng, lat]
  };
}

function transformPhysicalDetails(property: any) {
  const propertyData = property.propertyData || {};
  
  return {
    yearBuilt: propertyData.yearBuilt || property.yearBuilt ? parseInt(propertyData.yearBuilt || property.yearBuilt) : undefined,
    squareFootage: propertyData.SQFT || propertyData.usableSqft ? parseInt(propertyData.SQFT || propertyData.usableSqft) : undefined,
    units: propertyData.units ? parseInt(propertyData.units) : undefined,
    floors: propertyData.floors ? parseInt(propertyData.floors) : undefined,
    structures: propertyData.structures ? parseInt(propertyData.structures) : undefined,
    
    // Room details
    bedrooms: propertyData.bedroom ? parseInt(propertyData.bedroom) : undefined,
    bathrooms: propertyData.bathroom ? parseInt(propertyData.bathroom) : undefined,
    
    // Square footage breakdown
    buildingSquareFeet: propertyData.SQFT ? parseInt(propertyData.SQFT) : undefined,
    livingSquareFeet: propertyData.usableSqft ? parseInt(propertyData.usableSqft) : undefined,
    lotSquareFeet: propertyData.lot ? parseInt(propertyData.lot) : undefined,
    
    // Lot information
    lotSize: propertyData.lot || undefined,
    lotType: propertyData.lotType || undefined,
    lotAcres: propertyData.acres || undefined,
    
    // Construction details
    construction: propertyData.construction || property.type || undefined,
    primaryUse: propertyData.propertyValue || undefined,
    propertyUse: propertyData.buildingType || undefined,
    class: propertyData.class || undefined,
    
    // Parking & garage
    parking: propertyData.parking || undefined,
    
    // Utilities & systems
    meterType: propertyData.meterType || undefined,
    
    // Legal
    legalDescription: propertyData.parcelNo || undefined,
  };
}

function transformFinancials(property: any) {
  const propertyData = property.propertyData || {};
  const forsale = propertyData.forsale || {};
  const mostRecent = propertyData.mostrecentsale || {};
  
  return {
    // Current pricing
    price: forsale.price ? parseFloat(forsale.price) : undefined,
    pricePerSquareFoot: forsale.dollarPerSqFt || mostRecent.dollarPerSqFt || undefined,
    
    // Sale information
    saleDate: mostRecent.date ? new Date(mostRecent.date) : undefined,
    salePrice: mostRecent.price ? parseFloat(mostRecent.price) : undefined,
    lastSalePrice: mostRecent.price ? parseFloat(mostRecent.price) : undefined,
    lastSaleDate: mostRecent.date ? new Date(mostRecent.date) : undefined,
    
    // Property values
    landValue: propertyData.landValue || undefined,
    buildingValue: propertyData.bldgValue || undefined,
    
    // Investment metrics
    cap: forsale.cap || mostRecent.cap || undefined,
  };
}

function printProgress() {
  const elapsed = (Date.now() - startTime) / 1000;
  const rate = processed / elapsed;
  const remaining = totalProperties - processed;
  const eta = remaining / rate;
  
  console.log(`📊 Progress: ${((processed / totalProperties) * 100).toFixed(2)}% (${processed.toLocaleString()}/${totalProperties.toLocaleString()}) | ✅ ${successful} | ⏭️ ${skipped} | ❌ ${failed} | Rate: ${rate.toFixed(1)}/s | ETA: ${(eta/60).toFixed(1)}m`);
}

async function createMissingOrganizations() {
  console.log('🔍 Scanning for missing organizations...');
  
  // Get all existing organizations
  const existingOrgs = await prisma.organization.findMany({
    select: { id: true, name: true }
  });
  
  // Cache existing organizations
  for (const org of existingOrgs) {
    organizationCache.set(org.id, org.id);
  }
  
  console.log(`📋 Found ${existingOrgs.length} existing organizations`);
  
  // Get all unique team IDs from properties
  const uniqueTeamIds = await PropertyDetails.distinct('team');
  console.log(`📊 Found ${uniqueTeamIds.length} unique team IDs in properties`);
  
  const missingTeamIds = uniqueTeamIds.filter((teamId: any) => 
    teamId && !organizationCache.has(teamId.toString())
  );
  
  console.log(`📊 Found ${missingTeamIds.length} missing organizations to create`);
  
  // Create missing organizations in batches
  const batchSize = 10;
  for (let i = 0; i < missingTeamIds.length; i += batchSize) {
    const batch = missingTeamIds.slice(i, i + batchSize);
    const createPromises = batch.map((teamId: any) => 
      prisma.organization.create({
        data: {
          id: teamId.toString(),
          name: `Deleted Team (${teamId.toString().slice(-8)})`,
          createdAt: new Date(),
        }
      }).catch(error => {
        console.log(`⚠️ Error creating organization ${teamId}:`, error);
        return null;
      })
    );
    
    await Promise.all(createPromises);
    
    // Update cache
    for (const teamId of batch) {
      organizationCache.set(teamId.toString(), teamId.toString());
    }
    
    console.log(`✅ Created batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(missingTeamIds.length/batchSize)} of missing organizations`);
  }
}

async function createFallbackUser() {
  let fallbackUser = await prisma.user.findFirst({
    where: { email: '<EMAIL>' }
  });
  
  if (!fallbackUser) {
    console.log('👤 Creating fallback user for migration...');
    fallbackUser = await prisma.user.create({
      data: {
        id: new ObjectId().toString(),
        email: '<EMAIL>',
        name: 'PropBear Migration User',
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }
  
  return fallbackUser;
}

async function ensureOrganizationMembership(organizationId: string, fallbackUser: any) {
  // Check cache first
  const cacheKey = `${organizationId}:member`;
  if (userCache.has(cacheKey)) {
    return userCache.get(cacheKey);
  }
  
  // Find existing member
  const existingMember = await prisma.member.findFirst({
    where: { organizationId },
    select: { userId: true }
  });
  
  if (existingMember) {
    userCache.set(cacheKey, existingMember.userId);
    return existingMember.userId;
  }
  
  // Create fallback membership
  try {
    await prisma.member.create({
      data: {
        id: new ObjectId().toString(),
        userId: fallbackUser.id,
        organizationId,
        role: 'admin',
        createdAt: new Date(),
      }
    });
    userCache.set(cacheKey, fallbackUser.id);
    return fallbackUser.id;
  } catch (error) {
    console.log(`⚠️ Error creating fallback membership for ${organizationId}:`, error);
    return null;
  }
}

async function migratePropertyBatch(properties: any[], fallbackUser: any) {
  for (const property of properties) {
    try {
      processed++;
      
      // Check if organization exists
      const orgId = property.team?.toString();
      if (!orgId || !organizationCache.has(orgId)) {
        skipped++;
        continue;
      }
      
      // Ensure user membership
      const userId = await ensureOrganizationMembership(orgId, fallbackUser);
      if (!userId) {
        skipped++;
        continue;
      }
      
      // Transform the property data
      const transformedProperty = {
        id: property._id.toString(),
        name: property.name || 'Unnamed Property',
        recordType: 'property',
        organizationId: orgId,
        propertyType: transformPropertyType(property.type),
        status: property.status || 'active',
        createdBy: userId,
        updatedBy: userId,
        createdAt: property.createdAt || new Date(),
        updatedAt: property.updatedAt || new Date()
      };
      
      // Create the main property record
      const createdProperty = await prisma.property.create({
        data: transformedProperty
      });
      
      // Create related data
      
      // 1. Property Location - comprehensive location handling
      const address = transformAddress(property);
      let location = transformLocation(property.coordinates);
      
      // Check if we have meaningful address data (not just empty strings)
      const hasMeaningfulAddress = address.street.trim() || address.city.trim() || address.zip.trim();
      const hasCounty = property.county && property.county.trim();
      const hasParcel = property.propertyData?.parcelNo && property.propertyData.parcelNo.trim();
      const hasZoning = property.propertyData?.zoning && property.propertyData.zoning.trim();
      
      // Track coordinate source for debugging
      let coordinateSource = 'none';
      if (location) {
        coordinateSource = 'original';
      }
      
      // If no valid coordinates but we have address data, try geocoding (if API is working)
      if (!location && GOOGLE_API_KEY && (hasMeaningfulAddress || hasCounty)) {
        let geocodedCoords = null;
        
        if (hasMeaningfulAddress) {
          // Try full address first
          const addressString = `${address.street}, ${address.city}, ${address.state} ${address.zip}`.trim();
          geocodedCoords = await geocodeAddress(address);
          if (geocodedCoords) {
            coordinateSource = 'geocoded-address';
            geocodedCount++;
          }
        }
        
        // If address geocoding failed, try county as fallback
        if (!geocodedCoords && hasCounty) {
          geocodedCoords = await geocodeAddress({ county: property.county, country: 'US' });
          if (geocodedCoords) {
            coordinateSource = 'geocoded-county';
            geocodedCount++;
          }
        }
        
        if (geocodedCoords) {
          location = transformLocation(geocodedCoords);
        } else {
          // Skip geocoding warnings if API is not working to reduce noise
          if (processed % 1000 === 0) {
            console.log(`⚠️ Note: Geocoding not working (API not enabled) - creating address-only PropertyLocation records`);
          }
        }
      }
      
      // Create PropertyLocation if we have meaningful data - but handle null coordinates correctly
      const shouldCreateLocation = location || hasMeaningfulAddress || hasCounty || hasParcel || hasZoning;
      
      if (shouldCreateLocation) {
        // Create the PropertyLocation data object
        const propertyLocationData: any = {
          id: new ObjectId().toString(),
          propertyId: createdProperty.id,
        };
        
        // Only add location field if we have valid coordinates (avoid null location field)
        if (location) {
          propertyLocationData.location = location;
        }
        
        // Add other fields if they exist
        if (hasMeaningfulAddress) {
          propertyLocationData.address = address;
        }
        if (hasCounty) {
          propertyLocationData.county = property.county;
        }
        if (hasParcel) {
          propertyLocationData.parcelNumber = property.propertyData.parcelNo;
        }
        if (hasZoning) {
          propertyLocationData.zoning = property.propertyData.zoning;
        }
        
        await prisma.propertyLocation.create({
          data: propertyLocationData
        });
        
        // Log success with coordinate source every 100 properties
        if (processed % 100 === 0) {
          if (location) {
            console.log(`📍 Sample: ${property.name} - coordinates from ${coordinateSource}`);
          } else {
            console.log(`📍 Sample: ${property.name} - address only (no coordinates)`);
          }
        }
      }
      
      // 2. Property Physical Details
      const physicalDetails = transformPhysicalDetails(property);
      if (Object.values(physicalDetails).some(val => val !== undefined)) {
        await prisma.propertyPhysicalDetails.create({
          data: {
            id: new ObjectId().toString(),
            propertyId: createdProperty.id,
            ...physicalDetails,
          }
        });
      }
      
      // 3. Property Financials
      const financials = transformFinancials(property);
      if (Object.values(financials).some(val => val !== undefined)) {
        await prisma.propertyFinancials.create({
          data: {
            id: new ObjectId().toString(),
            propertyId: createdProperty.id,
            ...financials,
          }
        });
      }
      
      // 4. Property Flags (default values)
      await prisma.propertyFlags.create({
        data: {
          id: new ObjectId().toString(),
          propertyId: createdProperty.id,
        }
      });
      
      // 5. Unit Mixes (if any)
      if (property.propertyData?.unitMixes && property.propertyData.unitMixes.length > 0) {
        for (const unitMix of property.propertyData.unitMixes) {
          await prisma.propertyUnitMix.create({
            data: {
              id: new ObjectId().toString(),
              propertyId: createdProperty.id,
              organizationId: orgId,
              name: unitMix.unitType || 'Unknown',
              units: unitMix.noUnits ? parseFloat(unitMix.noUnits) : undefined,
              minSquareFootage: unitMix.unitMinSqft ? parseFloat(unitMix.unitMinSqft) : undefined,
              maxSquareFootage: unitMix.unitMaxSqft ? parseFloat(unitMix.unitMaxSqft) : undefined,
              minPrice: unitMix.unitMinPrice ? parseFloat(unitMix.unitMinPrice) : undefined,
              maxPrice: unitMix.unitMaxPrice ? parseFloat(unitMix.unitMaxPrice) : undefined,
              minRent: unitMix.unitMinRent ? parseFloat(unitMix.unitMinRent) : undefined,
              maxRent: unitMix.unitMaxRent ? parseFloat(unitMix.unitMaxRent) : undefined,
            }
          });
        }
      }
      
      successful++;
      
    } catch (error) {
      console.error(`❌ Error migrating property ${property.name}:`, error);
      failed++;
    }
  }
}

async function fullMigration() {
  try {
    console.log('🚀 Starting filtered properties migration...');
    
    // Specific organization IDs to migrate
    const targetOrganizations = [
      '628bff8d44cd3e01b746b737', // The Leeson Group  
      '628ea3ebfeec685660394d1c'  // The Holden Group
    ];
    
    console.log(`🎯 Targeting properties for ${targetOrganizations.length} specific organizations:`);
    targetOrganizations.forEach(orgId => console.log(`   - ${orgId}`));
    
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://pbdb:<EMAIL>/propbear?retryWrites=true&w=majority");
    console.log('✅ Connected to MongoDB');
    
    // Get total count for target organizations only
    const filter = { team: { $in: targetOrganizations.map(id => new ObjectId(id)) } };
    totalProperties = await PropertyDetails.countDocuments(filter);
    console.log(`📊 Total properties to migrate: ${totalProperties.toLocaleString()}`);
    
    // Ensure target organizations exist in Prisma
    const existingOrgs = await prisma.organization.findMany({
      where: { id: { in: targetOrganizations } },
      select: { id: true, name: true }
    });
    
    console.log(`📋 Found ${existingOrgs.length}/${targetOrganizations.length} target organizations in database:`);
    existingOrgs.forEach(org => console.log(`   - ${org.name} (${org.id})`));
    
    // Cache target organizations
    for (const org of existingOrgs) {
      organizationCache.set(org.id, org.id);
    }
    
    // Create fallback user
    const fallbackUser = await createFallbackUser();
    
    // Process properties in batches (only for target organizations)
    const batchSize = 100;
    let skip = 0;
    
    console.log('🔄 Starting filtered property migration...');
    
    while (skip < totalProperties) {
      const properties = await PropertyDetails.find(filter)
        .skip(skip)
        .limit(batchSize)
        .lean();
      
      if (properties.length === 0) break;
      
      await migratePropertyBatch(properties, fallbackUser);
      
      skip += batchSize;
      
      // Print progress every 50 properties (since we have fewer total)
      if (processed % 50 === 0 || properties.length < batchSize) {
        printProgress();
      }
      
      // Clear caches periodically to prevent memory issues
      if (processed % 1000 === 0) {
        userCache.clear();
        companyCache.clear();
        console.log('🧹 Cleared caches');
      }
    }
    
    console.log('\n📊 Final Migration Summary:');
    console.log(`🎯 Target Organizations: ${targetOrganizations.length}`);
    console.log(`✅ Successful: ${successful.toLocaleString()}`);
    console.log(`⏭️ Skipped: ${skipped.toLocaleString()}`);
    console.log(`❌ Failed: ${failed.toLocaleString()}`);
    console.log(`🗺️ Geocoded addresses: ${geocodedCount.toLocaleString()}`);
    console.log(`⏱️ Total time: ${((Date.now() - startTime) / 1000 / 60).toFixed(1)} minutes`);
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    await prisma.$disconnect();
  }
}

fullMigration().catch(console.error); 