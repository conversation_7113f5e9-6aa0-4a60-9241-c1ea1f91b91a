import { PrismaClient } from '@prisma/client';
import mongoose from 'mongoose';
import { ObjectId } from 'mongodb';
import PropertyDetails from '../models/properties.model.js';

const prisma = new PrismaClient();

// Property data transformation functions
function transformPropertyType(type?: string): string | undefined {
  if (!type) return undefined;
  
  // Map PropBear types to standardized types
  const typeMap: { [key: string]: string } = {
    'STUCCO': 'residential',
    'WOOD': 'residential', 
    'CONCRETE': 'commercial',
    'BRICK': 'residential',
    'STEEL': 'commercial',
    'MOBILE': 'residential'
  };
  
  return typeMap[type.toUpperCase()] || 'residential';
}

function transformAddress(property: any) {
  const address = {
    street: property.address || '',
    city: property.city || '',
    state: property.state || '',
    zip: property.zipCode?.toString() || '',
    county: property.county || '',
    country: 'US'
  };
  
  return address;
}

function transformLocation(coordinates?: { lat: number; lng: number }) {
  if (!coordinates || (coordinates.lat === 0 && coordinates.lng === 0)) {
    return null;
  }
  
  return {
    type: 'Point' as const,
    coordinates: [coordinates.lng, coordinates.lat]
  };
}

function transformPhysicalDetails(property: any) {
  const propertyData = property.propertyData || {};
  
  return {
    yearBuilt: propertyData.yearBuilt || property.yearBuilt ? parseInt(property.yearBuilt) : undefined,
    squareFootage: propertyData.SQFT || propertyData.usableSqft || undefined,
    units: propertyData.units || undefined,
    floors: propertyData.floors || undefined,
    structures: propertyData.structures ? parseInt(propertyData.structures) : undefined,
    
    // Room details
    bedrooms: propertyData.bedroom || undefined,
    bathrooms: propertyData.bathroom || undefined,
    
    // Square footage breakdown
    buildingSquareFeet: propertyData.SQFT || undefined,
    livingSquareFeet: propertyData.usableSqft || undefined,
    lotSquareFeet: propertyData.lot || undefined,
    
    // Lot information
    lotSize: propertyData.lot || undefined,
    lotType: propertyData.lotType || undefined,
    lotAcres: propertyData.acres || undefined,
    
    // Construction details
    construction: propertyData.construction || property.type || undefined,
    primaryUse: propertyData.propertyValue || undefined,
    propertyUse: propertyData.buildingType || undefined,
    class: propertyData.class || undefined,
    
    // Parking & garage
    parking: propertyData.parking || undefined,
    
    // Utilities & systems
    meterType: propertyData.meterType || undefined,
    
    // Legal
    legalDescription: propertyData.parcelNo || undefined,
  };
}

function transformFinancials(property: any) {
  const propertyData = property.propertyData || {};
  const forsale = propertyData.forsale || {};
  const mostRecent = propertyData.mostrecentsale || {};
  
  return {
    // Current pricing
    price: forsale.price ? parseFloat(forsale.price) : undefined,
    pricePerSquareFoot: forsale.dollarPerSqFt || mostRecent.dollarPerSqFt || undefined,
    
    // Sale information
    saleDate: mostRecent.date ? new Date(mostRecent.date) : undefined,
    salePrice: mostRecent.price ? parseFloat(mostRecent.price) : undefined,
    lastSalePrice: mostRecent.price ? parseFloat(mostRecent.price) : undefined,
    lastSaleDate: mostRecent.date ? new Date(mostRecent.date) : undefined,
    
    // Property values
    landValue: propertyData.landValue || undefined,
    buildingValue: propertyData.bldgValue || undefined,
    
    // Investment metrics
    cap: forsale.cap || mostRecent.cap || undefined,
  };
}

async function createMissingOrganizations(properties: any[]) {
  console.log('🔍 Scanning for missing organizations...');
  
  const existingOrgs = await prisma.organization.findMany({
    select: { id: true }
  });
  const existingOrgIds = new Set(existingOrgs.map(org => org.id));
  
  const missingTeamIds = new Set<string>();
  
  for (const property of properties) {
    const teamId = property.team?.toString();
    if (teamId && !existingOrgIds.has(teamId)) {
      missingTeamIds.add(teamId);
    }
  }
  
  console.log(`📊 Found ${missingTeamIds.size} missing organizations to create`);
  
  for (const teamId of missingTeamIds) {
    try {
      await prisma.organization.create({
        data: {
          id: teamId,
          name: `Deleted Team (${teamId.slice(-8)})`,
          createdAt: new Date(),
        }
      });
      console.log(`✅ Created missing organization: ${teamId}`);
    } catch (error) {
      console.log(`⚠️ Error creating organization ${teamId}:`, error);
    }
  }
}

async function testMigration() {
  try {
    console.log('🚀 Starting test properties migration...');
    
    // Connect to MongoDB
    await mongoose.connect("mongodb+srv://pbdb:<EMAIL>/propbear?retryWrites=true&w=majority");
    console.log('✅ Connected to MongoDB');
    
    // Use MongoDB model to get properties
    console.log('📖 Reading property data from MongoDB...');
    const properties = await PropertyDetails.find().limit(10).lean();
    
    console.log(`📊 Found ${properties.length} test properties to migrate`);
    
    // Log which team IDs are referenced
    const teamIds = properties.map(p => p.team?.toString()).filter(Boolean);
    console.log('🔍 Team IDs referenced by properties:', teamIds);
    
    // Create missing organizations first
    await createMissingOrganizations(properties);
    
    // Get updated organization mapping
    const organizations = await prisma.organization.findMany({
      select: { id: true, name: true }
    });
    
    const organizationMap = new Map<string, string>();
    for (const org of organizations) {
      organizationMap.set(org.id, org.id);
    }
    
    console.log(`📋 Found ${organizations.length} organizations (including newly created ones)`);
    
    // Create a fallback user for testing if needed
    let fallbackUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!fallbackUser) {
      console.log('👤 Creating fallback user for testing...');
      fallbackUser = await prisma.user.create({
        data: {
          id: new ObjectId().toString(),
          email: '<EMAIL>',
          name: 'Migration Test User',
          emailVerified: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
    
    let successful = 0;
    let skipped = 0;
    let failed = 0;
    
    for (const property of properties) {
      try {
        const propData = property as any; // MongoDB document
        
        // Check if organization exists
        const orgId = propData.team?.toString();
        if (!orgId || !organizationMap.has(orgId)) {
          console.log(`⏭️ Skipping property ${propData.name} - organization ${orgId} not found`);
          skipped++;
          continue;
        }
        
        // Find a user from the organization to use as creator
        let orgUsers = await prisma.member.findFirst({
          where: { organizationId: orgId },
          select: { userId: true }
        });
        
        // If no users found, create a membership with our fallback user
        if (!orgUsers) {
          console.log(`👤 Creating fallback membership for organization ${orgId}`);
          try {
            await prisma.member.create({
              data: {
                id: new ObjectId().toString(),
                userId: fallbackUser.id,
                organizationId: orgId,
                role: 'admin',
                createdAt: new Date(),
              }
            });
            orgUsers = { userId: fallbackUser.id };
          } catch (error) {
            console.log(`⚠️ Error creating fallback membership:`, error);
            console.log(`⏭️ Skipping property ${propData.name} - no users found in organization`);
            skipped++;
            continue;
          }
        }
        
        // Transform the property data
        const transformedProperty = {
          id: propData._id.toString(),
          name: propData.name || 'Unnamed Property',
          recordType: 'property',
          organizationId: orgId,
          propertyType: transformPropertyType(propData.type),
          status: propData.status || 'active',
          createdBy: orgUsers.userId,
          updatedBy: orgUsers.userId,
          createdAt: propData.createdAt || new Date(),
          updatedAt: propData.updatedAt || new Date()
        };
        
        // Create the main property record
        const createdProperty = await prisma.property.create({
          data: transformedProperty
        });
        
        // Create related data
        
        // 1. Property Location
        const address = transformAddress(propData);
        const location = transformLocation(propData.coordinates);
        
        if (address.street || address.city || location) {
          await prisma.propertyLocation.create({
            data: {
              id: new ObjectId().toString(),
              propertyId: createdProperty.id,
              address: address,
              location: location,
              county: propData.county || undefined,
              parcelNumber: propData.propertyData?.parcelNo || undefined,
              zoning: propData.propertyData?.zoning || undefined,
            }
          });
        }
        
        // 2. Property Physical Details
        const physicalDetails = transformPhysicalDetails(propData);
        if (Object.values(physicalDetails).some(val => val !== undefined)) {
          await prisma.propertyPhysicalDetails.create({
            data: {
              id: new ObjectId().toString(),
              propertyId: createdProperty.id,
              ...physicalDetails,
            }
          });
        }
        
        // 3. Property Financials
        const financials = transformFinancials(propData);
        if (Object.values(financials).some(val => val !== undefined)) {
          await prisma.propertyFinancials.create({
            data: {
              id: new ObjectId().toString(),
              propertyId: createdProperty.id,
              ...financials,
            }
          });
        }
        
        // 4. Property Flags (default values)
        await prisma.propertyFlags.create({
          data: {
            id: new ObjectId().toString(),
            propertyId: createdProperty.id,
          }
        });
        
        // 5. Unit Mixes (if any)
        if (propData.propertyData?.unitMixes && propData.propertyData.unitMixes.length > 0) {
          for (const unitMix of propData.propertyData.unitMixes) {
            await prisma.propertyUnitMix.create({
              data: {
                id: new ObjectId().toString(),
                propertyId: createdProperty.id,
                organizationId: orgId,
                name: unitMix.unitType || 'Unknown',
                units: unitMix.noUnits ? parseFloat(unitMix.noUnits) : undefined,
                minSquareFootage: unitMix.unitMinSqft ? parseFloat(unitMix.unitMinSqft) : undefined,
                maxSquareFootage: unitMix.unitMaxSqft ? parseFloat(unitMix.unitMaxSqft) : undefined,
                minPrice: unitMix.unitMinPrice ? parseFloat(unitMix.unitMinPrice) : undefined,
                maxPrice: unitMix.unitMaxPrice ? parseFloat(unitMix.unitMaxPrice) : undefined,
                minRent: unitMix.unitMinRent ? parseFloat(unitMix.unitMinRent) : undefined,
                maxRent: unitMix.unitMaxRent ? parseFloat(unitMix.unitMaxRent) : undefined,
              }
            });
          }
        }
        
        console.log(`✅ Successfully migrated property: ${propData.name}`);
        successful++;
        
      } catch (error) {
        console.error(`❌ Error migrating property ${property.name}:`, error);
        failed++;
      }
    }
    
    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successful: ${successful}`);
    console.log(`⏭️ Skipped: ${skipped}`);
    console.log(`❌ Failed: ${failed}`);
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    await prisma.$disconnect();
  }
}

testMigration().catch(console.error); 