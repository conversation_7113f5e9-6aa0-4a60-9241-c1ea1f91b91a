import fs from 'fs';

async function examinePropertyData() {
  try {
    console.log('Reading property data file...');
    
    // Read just a small chunk to examine structure
    const buffer = Buffer.alloc(50000); // Read first 50KB
    const fd = fs.openSync('./data/propbear.propertydetails.json', 'r');
    const bytesRead = fs.readSync(fd, buffer, 0, 50000, 0);
    fs.closeSync(fd);
    
    const chunk = buffer.toString('utf8', 0, bytesRead);
    console.log('Raw data sample:');
    console.log(chunk.substring(0, 1000));
    
    // Try to extract the first complete record
    const records = chunk.split('},');
    if (records.length > 0) {
      let firstRecordStr = records[0];
      if (firstRecordStr.startsWith('[')) {
        firstRecordStr = firstRecordStr.substring(1);
      }
      firstRecordStr += '}'; // Add back the closing brace
      
      try {
        const firstRecord = JSON.parse(firstRecordStr);
        console.log('\n=== PROPERTY STRUCTURE ===');
        console.log('Property keys:');
        console.log(Object.keys(firstRecord).sort());
        
        console.log('\n=== SAMPLE VALUES ===');
        Object.keys(firstRecord).slice(0, 20).forEach(key => {
          const value = firstRecord[key];
          console.log(`${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`);
        });
        
      } catch (e) {
        console.log('Error parsing first record:', e);
        console.log('First record string:', firstRecordStr.substring(0, 500));
      }
    }
    
  } catch (error) {
    console.error('Error reading file:', error);
  }
}

examinePropertyData().catch(console.error); 