import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	getDbTable,
	OBJECT_CONFIG,
	type ObjectType,
	normalizeObjectType,
} from "./config";
import { z } from "zod";

export const objectsInfiniteRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Middleware to validate object type for infinite routes only
const validateObjectTypeMiddleware = async (c: any, next: any) => {
	const objectTypeParam = c.req.param("objectType");
	const objectType = normalizeObjectType(objectTypeParam);

	if (!objectType) {
		return c.json({ error: "Invalid object type" }, 400);
	}

	c.set("objectType" as any, objectType);
	await next();
};

// Schema for viewport-based property fetching
const viewportQuerySchema = z.object({
	organizationId: z.string().min(1),
	bounds: z.object({
		north: z.number(),
		south: z.number(),
		east: z.number(),
		west: z.number(),
	}),
	zoom: z.number().min(0).max(22),
	limit: z.number().int().positive().max(5000).optional().default(1000),
	clusterZoom: z.number().min(0).max(22).optional(),
});

// Infinite query endpoint
objectsInfiniteRouter.get(
	"/objects/:objectType/infinite",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;

			// Parse query parameters
			const organizationId = c.req.query("organizationId");
			const limit = Math.min(
				Number.parseInt(c.req.query("limit") || "50"),
				100,
			);
			const offset = Number.parseInt(c.req.query("offset") || "0");
			const cursor = c.req.query("cursor");
			const direction = c.req.query("direction") || "next";
			const search = c.req.query("search");
			const createdAt = c.req.query("createdAt");

			// Determine pagination mode based on presence of offset vs cursor
			const isPaginationMode = offset !== undefined && offset >= 0 && !cursor;

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

			// Build base where conditions
			const whereConditions: any = {
				organizationId,
				isDeleted: false,
			};

			// Add object-specific filters based on query parameters
			logger.info(`Received query parameters for ${objectType}:`, c.req.query());
			Object.keys(config.filterFields).forEach((field) => {
				const value = c.req.query(field);
				if (value && field !== "createdAt") {
					logger.info(`Processing filter: ${field} = ${value}`);
					const filterType = (config.filterFields as any)[field];

					if (filterType === "string") {
						if (field === "name" && objectType === "contact") {
							// Special handling for name in contacts
							whereConditions.OR = [
								{
									firstName: {
										contains: value,
										mode: "insensitive",
									},
								},
								{
									lastName: {
										contains: value,
										mode: "insensitive",
									},
								},
							];
						} else {
							whereConditions[field] = {
								contains: value,
								mode: "insensitive",
							};
						}
					} else if (filterType === "nested") {
						// Handle nested fields like address.location.street for properties
						if (field.startsWith("address.location.") && objectType === "property") {
							// For address fields, we'll need to filter in memory after the main query
							// since MongoDB JSON queries with Prisma have syntax issues
							// Mark this for post-query filtering
							if (!whereConditions._addressFilters) {
								whereConditions._addressFilters = {};
							}
							const addressField = field.replace("address.location.", "");
							whereConditions._addressFilters[addressField] = value;
						}
					} else if (filterType === "number") {
						// Handle numeric filters (can be ranges)
						try {
							if (value.includes(",")) {
								// Range filter like "100000,500000"
								const [min, max] = value.split(",").map(Number);
								if (!isNaN(min) || !isNaN(max)) {
									const numericFilter: any = {};
									if (!isNaN(min)) numericFilter.gte = min;
									if (!isNaN(max)) numericFilter.lte = max;
									
									// Map to correct nested relations for property fields
									if (objectType === "property") {
										if (field === "price") {
											whereConditions.financials = {
												...whereConditions.financials,
												price: numericFilter,
											};
										} else if (["bedrooms", "bathrooms", "squareFootage", "yearBuilt", "lotSize"].includes(field)) {
											whereConditions.physicalDetails = {
												...whereConditions.physicalDetails,
												[field]: numericFilter,
											};
										} else {
											whereConditions[field] = numericFilter;
										}
									} else {
										whereConditions[field] = numericFilter;
									}
								}
							} else {
								// Single value filter
								const numValue = Number(value);
								if (!isNaN(numValue)) {
									if (objectType === "property") {
										if (field === "price") {
											whereConditions.financials = {
												...whereConditions.financials,
												price: numValue,
											};
										} else if (["bedrooms", "bathrooms", "squareFootage", "yearBuilt", "lotSize"].includes(field)) {
											whereConditions.physicalDetails = {
												...whereConditions.physicalDetails,
												[field]: numValue,
											};
										} else {
											whereConditions[field] = numValue;
										}
									} else {
										whereConditions[field] = numValue;
									}
								}
							}
						} catch (error) {
							logger.warn(`Invalid numeric filter value for ${field}:`, value);
						}
					} else if (
						filterType === "relation" &&
						field === "company" &&
						objectType === "contact"
					) {
						whereConditions.company = {
							name: { contains: value, mode: "insensitive" },
						};
					} else if (filterType === "nested" && objectType === "property") {
						// Handle nested address fields for properties
						if (field.startsWith("address.location.")) {
							const addressField = field.replace("address.location.", "");
							whereConditions.location = {
								...whereConditions.location,
								address: {
									path: [addressField],
									string_contains: value,
								},
							};
						}
					} else {
						// Handle array filters like propertyType and status
						if (filterType === "stringArray" || Array.isArray(value) || value.includes(",")) {
							const values = Array.isArray(value) ? value : value.split(",").map((v: string) => v.trim()).filter(Boolean);
							if (values.length > 0) {
								whereConditions[field] = {
									in: values,
								};
							}
						} else {
							whereConditions[field] = value;
						}
					}
				}
			});

			// Handle additional query parameters not in config.filterFields
			// This handles cases where frontend sends field names that don't match backend config
			const allQueryParams = c.req.query();
			const configuredFields = Object.keys(config.filterFields);
			Object.keys(allQueryParams).forEach((queryField) => {
				const value = allQueryParams[queryField];
				if (value && !configuredFields.includes(queryField) && 
					!["organizationId", "limit", "offset", "cursor", "direction", "search", "createdAt", "tags", "size", "start", "sort", "id", "live"].includes(queryField)) {
					
					// Handle address.location.* fields for properties
					if (objectType === "property" && queryField.startsWith("address.location.")) {
						const addressField = queryField.replace("address.location.", "");

						// Mark for post-query filtering instead of direct database query
						if (!whereConditions._addressFilters) {
							whereConditions._addressFilters = {};
						}
						whereConditions._addressFilters[addressField] = value;
					}
					// Handle legacy address.* fields for properties (backward compatibility)
					else if (objectType === "property" && queryField.startsWith("address.") && !queryField.startsWith("address.location.")) {
						const addressField = queryField.replace("address.", "");

						// Mark for post-query filtering instead of direct database query
						if (!whereConditions._addressFilters) {
							whereConditions._addressFilters = {};
						}
						whereConditions._addressFilters[addressField] = value;
					}
					// Handle location.address.* fields for properties (backward compatibility)
					else if (objectType === "property" && queryField.startsWith("location.address.")) {
						const addressField = queryField.replace("location.address.", "");

						// Mark for post-query filtering instead of direct database query
						if (!whereConditions._addressFilters) {
							whereConditions._addressFilters = {};
						}
						whereConditions._addressFilters[addressField] = value;
					}
				}
			});

			// Handle tags filtering
			const tags = c.req.query("tags");
			if (tags) {
				const tagNames = tags.split(",").map(tag => tag.trim()).filter(Boolean);
				if (tagNames.length > 0) {
					whereConditions.objectTags = {
						some: {
							tag: {
								name: {
									in: tagNames, // Use tag names directly without normalization for now
								},
								organizationId,
							},
						},
					};
				}
			}

			// Handle search across configured search fields
			if (search) {
				whereConditions.OR = config.searchFields.map((field) => ({
					[field]: { contains: search, mode: "insensitive" },
				}));
			}

			// Handle createdAt date range filter
			if (createdAt) {
				try {
					let startDate: Date | null = null;
					let endDate: Date | null = null;

					// Helper function to parse date string
					const parseDate = (dateStr: string): Date | null => {
						try {
							// Try parsing as timestamp first
							if (/^\d+$/.test(dateStr.trim())) {
								const date = new Date(Number.parseInt(dateStr.trim()));
								if (!isNaN(date.getTime())) return date;
							}
							
							// Try parsing as ISO string
							const date = new Date(dateStr.trim());
							if (!isNaN(date.getTime())) return date;
							
							return null;
						} catch (error) {
							logger.error("Error parsing date:", { dateStr, error });
							return null;
						}
					};

					if (createdAt.includes(",")) {
						// Handle comma-separated date strings (from frontend date picker)
						const [startString, endString] = createdAt.split(",");
						startDate = parseDate(startString);
						endDate = parseDate(endString);
					} else if (createdAt.includes(":")) {
						// Handle colon-separated timestamps (from URL parameters)
						const [startString, endString] = createdAt.split(":");
						startDate = parseDate(startString);
						endDate = parseDate(endString);
					} else {
						// Single date/timestamp
						const date = parseDate(createdAt);
						if (date) {
							startDate = new Date(date);
							startDate.setUTCHours(0, 0, 0, 0);
							endDate = new Date(date);
							endDate.setUTCHours(23, 59, 59, 999);
						}
					}

					// Only add date filter if we have valid dates
					if (startDate && endDate) {
						whereConditions.createdAt = {
							gte: startDate,
							lte: endDate,
						};
					} else {
						logger.warn("Invalid date range:", { createdAt });
					}
				} catch (error) {
					logger.error("Error parsing createdAt filter:", error);
				}
			}

			// Build final where conditions
			const finalWhereConditions = {
				organizationId,
				isDeleted: false,
				...whereConditions,
			};

			// Extract address filters for in-memory filtering (remove from DB query)
			const addressFilters = finalWhereConditions._addressFilters;
			delete finalWhereConditions._addressFilters;

			// Add cursor conditions for pagination (but only for infinite scroll mode)
			if (cursor && !createdAt && !isPaginationMode) {
				// Only apply cursor pagination if no date filter is active and not in pagination mode
				const cursorDate = new Date(cursor);
				const cursorCondition =
					direction === "next"
						? { lt: cursorDate }
						: { gt: cursorDate };

				finalWhereConditions.createdAt = cursorCondition;
			}
			
			// Configure query options based on pagination mode
			const queryOptions: any = {
				where: finalWhereConditions,
				include: config.include,
				orderBy: { createdAt: direction === "next" ? "desc" : "asc" },
				cacheStrategy: {
					ttl: 180, // 3 minutes cache for infinite scroll (more dynamic)
					tags: [`infinite_${objectType}_org_${organizationId}`, `org_${organizationId}`],
				},
			};

			if (isPaginationMode) {
				// For paginated queries, use skip and take
				queryOptions.skip = offset;
				queryOptions.take = limit;
			} else {
				// For infinite scroll, take one extra to check if there are more
				queryOptions.take = limit + 1;
			}

			// Fetch results
			const results = await table.findMany(queryOptions);

			// Apply in-memory address filtering for properties if needed
			let filteredResults = results;
			if (objectType === "property" && addressFilters && Object.keys(addressFilters).length > 0) {
				filteredResults = results.filter((property: any) => {
					if (!property.location?.address) return false;
					
					const address = property.location.address as any;
					
					// Check all address filter conditions
					return Object.entries(addressFilters).every(([field, value]) => {
						const addressValue = address[field];
						if (!addressValue) return false;
						
						// Case-insensitive contains search
						return String(addressValue).toLowerCase().includes(String(value).toLowerCase());
					});
				});
			}

			// Get total count (unfiltered)
			const total = await table.count({
				where: {
					organizationId,
					isDeleted: false,
				},
				cacheStrategy: {
					ttl: 300, // 5 minutes cache for count
					tags: [`total_count_${objectType}_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			// Get filtered count (using the same conditions as the main query)
			const filteredTotal = await table.count({
				where: finalWhereConditions,
				cacheStrategy: {
					ttl: 180, // 3 minutes cache for filtered count (more dynamic)
					tags: [`filtered_count_${objectType}_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			// Adjust filtered count if we applied in-memory address filtering
			let actualFilteredTotal = filteredTotal;
			if (objectType === "property" && addressFilters && Object.keys(addressFilters).length > 0) {
				// For address filtering, we need to count all matching records, not just the current page
				// This is approximate since we can't easily count without fetching all records
				actualFilteredTotal = filteredResults.length;
				
				// If we're in pagination mode and have a full page, estimate total
				if (isPaginationMode && filteredResults.length === limit) {
					// This is an approximation - in a real scenario you'd want to do a separate count query
					actualFilteredTotal = Math.max(filteredTotal, offset + filteredResults.length);
				}
			}

			let hasMore: boolean;
			let data: any[];

			if (isPaginationMode) {
				// For pagination mode, no need to check for extra records
				data = filteredResults;
				hasMore = offset + limit < actualFilteredTotal;
			} else {
				// For infinite scroll mode, check if we have extra records
				hasMore = filteredResults.length > limit;
				data = hasMore ? filteredResults.slice(0, limit) : filteredResults;
			}

			// When date filtering is active or in pagination mode, disable cursor-based pagination
			const nextCursor =
				hasMore && data.length > 0 && !createdAt && !isPaginationMode
					? data[data.length - 1]?.createdAt?.toISOString()
					: null;

			const prevCursor =
				data.length > 0 && !createdAt && !isPaginationMode
					? data[0]?.createdAt?.toISOString()
					: null;

			// Transform results using object-specific transformer
			const transformedData = data.map((item: any) => config.transformResult(item));

			// Calculate facets for all filters
			const facets: Record<string, any> = {};

			// Generate tags facets for all object types that support tags
			// First, get all available tags for this organization and object type
			const allTags = await db.tag.findMany({
				where: {
					organizationId,
					objectType,
				},
				select: {
					id: true,
					name: true,
				},
				cacheStrategy: {
					ttl: 600, // 10 minutes cache for tags
					tags: [`all_tags_${objectType}_org_${organizationId}`, `org_${organizationId}`],
				},
			});

			if (allTags.length > 0) {
				// Get usage counts for tags that are actually attached to objects
				const tagUsageCounts = await db.objectTag.groupBy({
					by: ["tagId"],
					where: {
						objectType,
						tag: {
							organizationId,
						},
					},
					_count: { tagId: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets
						tags: [`facets_tags_${objectType}_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				// Create a map of tag usage counts
				const usageCountsMap = new Map(
					tagUsageCounts.map((item: any) => [item.tagId, item._count.tagId])
				);

				// Generate facet rows for all tags, showing 0 count for unused tags
				const facetRows = allTags.map((tag) => ({
					value: tag.name,
					total: usageCountsMap.get(tag.id) || 0,
				}));
				
				facets.tags = {
					rows: facetRows,
					total: tagUsageCounts.reduce(
						(sum: number, item: any) =>
							sum + (item._count.tagId as number),
						0,
					),
				};
			}

			if (objectType === "contact") {
				const statusCounts = await db.contact.groupBy({
					by: ["status"],
					where: { organizationId, isDeleted: false },
					_count: { status: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets (relatively stable)
						tags: [`facets_contact_status_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				facets.status = {
					rows: statusCounts.map((item: any) => ({
						value: (item.status as string) || "Unknown",
						total: (item._count.status as number) || 0,
					})),
					min: null,
					max: null,
				};
			} else if (objectType === "property") {
				// Property type facet
				const propertyTypeFacet = await db.property.groupBy({
					by: ["propertyType"],
					where: {
						organizationId,
						isDeleted: false,
						propertyType: { not: null },
					},
					_count: { propertyType: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets
						tags: [`facets_property_type_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				facets.propertyType = {
					rows: propertyTypeFacet.map((item: any) => ({
						value: (item.propertyType as string) || "Unknown",
						total: (item._count.propertyType as number) || 0,
					})),
					total: propertyTypeFacet.reduce(
						(sum: number, item: any) =>
							sum + item._count.propertyType,
						0,
					),
				};

				// Status facet
				const statusFacet = await db.property.groupBy({
					by: ["status"],
					where: {
						organizationId,
						isDeleted: false,
						status: { not: null },
					},
					_count: { status: true },
					cacheStrategy: {
						ttl: 600, // 10 minutes cache for facets
						tags: [`facets_property_status_org_${organizationId}`, `org_${organizationId}`],
					},
				});

				facets.status = {
					rows: statusFacet.map((item: any) => ({
						value: (item.status as string) || "Unknown",
						total: (item._count.status as number) || 0,
					})),
					total: statusFacet.reduce(
						(sum: number, item: any) =>
							sum + (item._count.status as number),
						0,
					),
				};
			}

			return c.json({
				data: transformedData,
				meta: {
					totalRowCount: total,
					filterRowCount: actualFilteredTotal,
					chartData: [],
					facets,
					metadata: {
						objectType,
						totalCount: total,
						hasNextPage: hasMore,
					},
				},
				prevCursor,
				nextCursor,
			});
		} catch (error) {
			logger.error("Failed to fetch infinite data:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

// Test endpoint to verify the route is working (no auth required for testing)
objectsInfiniteRouter.get(
	"/objects/properties/viewport/test",
	async (c) => {
		return c.json({ 
			message: "Viewport endpoint is accessible", 
			timestamp: new Date().toISOString(),
			headers: c.req.header(),
		});
	}
);

// Test endpoint with auth
objectsInfiniteRouter.get(
	"/objects/properties/viewport/test-auth",
	authMiddleware,
	async (c) => {
		const user = c.get("user");
		return c.json({ 
			message: "Authenticated viewport endpoint is accessible", 
			timestamp: new Date().toISOString(),
			userId: user?.id,
		});
	}
);

// Viewport-based endpoint for map optimization
objectsInfiniteRouter.post(
	"/objects/properties/viewport",
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			let body;
			try {
				body = await c.req.json();
			} catch (jsonError) {
				logger.error("Invalid JSON in request body:", jsonError);
				return c.json({ error: "Invalid JSON in request body" }, 400);
			}

			let parsedParams;
			try {
				parsedParams = viewportQuerySchema.parse(body);
			} catch (parseError) {
				logger.error("Invalid request parameters:", parseError);
				return c.json({ 
					error: "Invalid request parameters", 
					details: parseError instanceof Error ? parseError.message : "Unknown validation error"
				}, 400);
			}

			const { organizationId, bounds, zoom, limit, clusterZoom } = parsedParams;

			try {
				await verifyOrganizationMembership(organizationId, user.id);
			} catch (membershipError) {
				logger.error("Organization membership verification failed:", membershipError);
				return c.json({ error: "Unauthorized access to organization" }, 403);
			}

			// Determine if we should use clustering based on zoom level and data density
			const shouldCluster = zoom < 14 || clusterZoom !== undefined;
			const effectiveLimit = shouldCluster ? Math.min(limit * 10, 10000) : limit;

			logger.info("Fetching properties for viewport", {
				organizationId,
				bounds,
				zoom,
				effectiveLimit,
				shouldCluster,
			});

			// Fetch properties with location data
			let properties;
			try {
				properties = await db.property.findMany({
					where: {
						organizationId,
						isDeleted: false,
					},
					include: {
						location: true, // Include full location relation
						physicalDetails: true,
						financials: true,
						flags: true,
						mlsData: true,
						legalInfo: true,
						demographics: true,
						creator: {
							select: {
								id: true,
								name: true,
								email: true,
								image: true,
							},
						},
					},
					take: effectiveLimit,
					orderBy: { createdAt: "desc" },
				});
			} catch (dbError) {
				logger.error("Database query failed:", dbError);
				return c.json({ 
					error: "Database query failed", 
					details: dbError instanceof Error ? dbError.message : "Unknown database error"
				}, 500);
			}

			// Filter properties by viewport bounds
			const filteredProperties = properties.filter((property: any) => {
				// Check if property has location with coordinates
				if (!property.location?.location) return false;
				
				const location = property.location.location as any;
				const coordinates = location?.coordinates;
				
				if (!Array.isArray(coordinates) || coordinates.length !== 2) return false;
				
				const [lng, lat] = coordinates;
				
				// Check if coordinates are within bounds
				return lng >= bounds.west && lng <= bounds.east && 
					   lat >= bounds.south && lat <= bounds.north;
			});

			logger.info("Properties after filtering", {
				total: properties.length,
				filtered: filteredProperties.length,
				bounds,
			});

			// Apply clustering if zoom level is low
			let resultData: any[] = filteredProperties;
			
			if (shouldCluster && filteredProperties.length > 0) {
				try {
					resultData = clusterProperties(filteredProperties, zoom, clusterZoom || zoom);
					logger.info("Clustering applied", {
						originalCount: filteredProperties.length,
						clusteredCount: resultData.length,
					});
				} catch (clusterError) {
					logger.error("Clustering failed:", clusterError);
					// Fall back to original data
					resultData = filteredProperties;
				}
			}

			// Transform results - simplified approach
			let transformedData: any[];
			try {
				const config = OBJECT_CONFIG.property;
				transformedData = resultData.map(item => {
					if (item.isCluster) {
						return item; // Return cluster as-is
					}
					
					// Simplified transformation to avoid complex config issues
					return {
						id: item.id,
						name: item.name,
						organizationId: item.organizationId,
						recordType: "property",
						propertyType: item.propertyType,
						status: item.status,
						location: item.location,
						createdAt: item.createdAt,
						updatedAt: item.updatedAt,
						isDeleted: item.isDeleted,
						// Add other essential fields
						tags: [],
						lists: [],
						linkedContacts: [],
						linkedCompanies: [],
						tasks: [],
						creator: item.creator,
					};
				});
			} catch (transformError) {
				logger.error("Transformation failed:", transformError);
				// Return minimal data structure
				transformedData = resultData.map(item => ({
					id: item.id,
					name: item.name,
					location: item.location,
					propertyType: item.propertyType,
					isCluster: item.isCluster || false,
				}));
			}

			// Limit final results
			const finalData = transformedData.slice(0, limit);

			logger.info(`Viewport query returned ${finalData.length} properties/clusters for zoom ${zoom}`, {
				organizationId,
				bounds,
				zoom,
				originalCount: filteredProperties.length,
				clustered: shouldCluster,
			});

			return c.json({
				data: finalData,
				meta: {
					totalInViewport: filteredProperties.length,
					bounds,
					zoom,
					clustered: shouldCluster,
				},
			});
		} catch (error) {
			logger.error("Failed to fetch viewport properties:", error);
			return c.json({ 
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error"
			}, 500);
		}
	},
);

// Clustering algorithm for map optimization
function clusterProperties(properties: any[], zoom: number, clusterZoom?: number): any[] {
	const effectiveZoom = clusterZoom || zoom;
	
	// Determine cluster size based on zoom level
	const getClusterDistance = (zoomLevel: number): number => {
		if (zoomLevel <= 8) return 0.1;    // ~11km at equator
		if (zoomLevel <= 10) return 0.05;   // ~5.5km at equator
		if (zoomLevel <= 12) return 0.02;   // ~2.2km at equator
		if (zoomLevel <= 14) return 0.01;   // ~1.1km at equator
		return 0.005;                       // ~550m at equator
	};

	const clusterDistance = getClusterDistance(effectiveZoom);
	const clusters: any[] = [];
	const processed = new Set<string>();

	for (const property of properties) {
		if (processed.has(property.id)) continue;

		const location = property.location?.location as any;
		if (!location?.coordinates) continue;

		const [lng, lat] = location.coordinates;
		const cluster = {
			id: `cluster_${property.id}`,
			isCluster: true,
			position: [lng, lat],
			count: 1,
			properties: [property],
			bounds: { north: lat, south: lat, east: lng, west: lng },
			propertyTypes: new Set([property.propertyType]),
			averagePrice: property.financials?.currentPrice || 0,
			zoom: effectiveZoom,
		};

		processed.add(property.id);

		// Find nearby properties to cluster
		for (const otherProperty of properties) {
			if (processed.has(otherProperty.id)) continue;

			const otherLocation = otherProperty.location?.location as any;
			if (!otherLocation?.coordinates) continue;

			const [otherLng, otherLat] = otherLocation.coordinates;
			const distance = calculateDistance(lng, lat, otherLng, otherLat);

			if (distance <= clusterDistance) {
				cluster.count++;
				cluster.properties.push(otherProperty);
				cluster.bounds.north = Math.max(cluster.bounds.north, otherLat);
				cluster.bounds.south = Math.min(cluster.bounds.south, otherLat);
				cluster.bounds.east = Math.max(cluster.bounds.east, otherLng);
				cluster.bounds.west = Math.min(cluster.bounds.west, otherLng);
				cluster.propertyTypes.add(otherProperty.propertyType);
				
				const price = otherProperty.financials?.currentPrice || 0;
				cluster.averagePrice = (cluster.averagePrice * (cluster.count - 1) + price) / cluster.count;
				
				processed.add(otherProperty.id);
			}
		}

		// Calculate cluster center (centroid)
		if (cluster.count > 1) {
			const centerLng = cluster.properties.reduce((sum, p) => {
				const coords = (p.location?.location as any)?.coordinates;
				return sum + (coords?.[0] || 0);
			}, 0) / cluster.count;
			
			const centerLat = cluster.properties.reduce((sum, p) => {
				const coords = (p.location?.location as any)?.coordinates;
				return sum + (coords?.[1] || 0);
			}, 0) / cluster.count;
			
			cluster.position = [centerLng, centerLat];
		}

		clusters.push(cluster);
	}

	return clusters;
}

// Haversine distance calculation for clustering
function calculateDistance(lng1: number, lat1: number, lng2: number, lat2: number): number {
	const R = 6371; // Earth's radius in km
	const dLat = (lat2 - lat1) * Math.PI / 180;
	const dLng = (lng2 - lng1) * Math.PI / 180;
	const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
			  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
			  Math.sin(dLng / 2) * Math.sin(dLng / 2);
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
	return R * c; // Distance in km
}

export type ObjectsInfiniteRouter = typeof objectsInfiniteRouter;
