"use client";

import { fetchFavorites, useToggleFavorite } from "@app/favorites/lib/api";
import { useQuery } from "@tanstack/react-query";
import { useQueryStates } from "nuqs";
import { useMemo } from "react";
import { toast } from "sonner";
import type {
	BaseObjectSchema,
	ObjectConfig,
} from "../../../../object-views/lib/types";
import { DataTableInfinite } from "../DataTableInfinite";
import { createGenericApiHooks } from "./api-factory";
import { FieldType } from "@repo/database";
import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

interface DataTablePaginatedProps<TData extends BaseObjectSchema, TMeta = any> {
	organizationId: string;
	config: ObjectConfig<TData, TMeta>;
	renderHeader?: () => React.ReactNode;
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
			visible?: boolean;
		}>;
		filters?: Array<{
			field: string;
			logic: string;
			text?: string;
			number?: number | number[];
		}>;
	};
	onUpdateView?: (updatedView: any) => void;
}

// Hook to fetch paginated data
function usePaginatedData<TData extends BaseObjectSchema>(
	organizationId: string,
	objectType: string,
	search: any,
) {
	return useQuery({
		queryKey: [`${objectType}-paginated`, organizationId, search],
		queryFn: async () => {
			if (!organizationId) return null;

			const params = new URLSearchParams();
			params.append("organizationId", organizationId);
			
			// Add pagination params
			const limit = search.size || 50; // Default page size to match API default
			const offset = search.start || 0; // Use offset directly
			params.append("limit", limit.toString());
			params.append("offset", offset.toString());

			// Add search and filters - exclude pagination and system params
			Object.entries(search).forEach(([key, value]) => {
				if (value !== null && value !== undefined && value !== "" && 
					!["size", "start", "sort", "id", "cursor", "direction", "live"].includes(key)) {
					
					if (key === "createdAt" && Array.isArray(value)) {
						// Convert Date objects to timestamps and join with colon
						const timestamps = value.map((date) =>
							date instanceof Date ? date.getTime() : date,
						);
						params.append(key, timestamps.join(":"));
					} else if (Array.isArray(value)) {
						// Handle array values (like tags, propertyType, etc.)
						params.append(key, value.join(","));
					} else {
						params.append(key, String(value));
					}
				}
			});

			// Use the infinite API endpoint which has proper filtering support
			const response = await fetch(`/api/objects/${objectType}/infinite?${params.toString()}`, {
				credentials: "include",
			});

			if (!response.ok) {
				throw new Error("Failed to fetch data");
			}

			const result = await response.json();
			
			// Transform infinite API response to pagination format
			const data = result.data || [];
			const meta = result.meta || {};
			
			return {
				data,
				pagination: {
					total: meta.totalRowCount || 0,
					filteredTotal: meta.filterRowCount || 0,
					limit,
					offset,
					hasMore: offset + limit < (meta.filterRowCount || 0),
				},
				facets: meta.facets || {},
			};
		},
		enabled: !!organizationId,
	});
}

export function DataTablePaginated<
	TData extends BaseObjectSchema,
	TMeta = any,
>({
	organizationId,
	config,
	renderHeader,
	view,
	onUpdateView,
}: DataTablePaginatedProps<TData, TMeta>) {
	const [search, setSearch] = useQueryStates(config.searchParamsParser);

	// Create generic API hooks for this object type
	const apiHooks = useMemo(
		() =>
			createGenericApiHooks<TData>({
				objectType: config.type,
				apiEndpoint: config.apiEndpoint,
			}),
		[config.type, config.apiEndpoint],
	);

	// Get mutations for cell-level operations
	const updateFieldMutation = apiHooks.useUpdateField();
	const clearFieldMutation = apiHooks.useClearField();
	const deleteMutation = apiHooks.useDeleteRecord();
	const toggleFavoriteMutation = useToggleFavorite(organizationId);

	// Fetch favorites to check status
	const { data: favorites = [] } = useQuery({
		queryKey: ["favorites", organizationId],
		queryFn: () =>
			organizationId
				? fetchFavorites(organizationId)
				: Promise.resolve([]),
		enabled: !!organizationId,
	});

	// Fetch paginated data
	const {
		data: queryResult,
		isFetching,
		isLoading,
		refetch,
	} = usePaginatedData<TData>(organizationId, config.type, search);

	const flatData = queryResult?.data || [];
	const pagination = queryResult?.pagination || { total: 0, filteredTotal: 0, limit: 25, offset: 0, hasMore: false };
	const facets = queryResult?.facets || {};

	// Calculate pagination info
	const currentPage = Math.floor(pagination.offset / pagination.limit) + 1;
	const totalPages = Math.ceil(pagination.total / pagination.limit);

	// Convert view filters to table column filter format
	const convertedViewFilters = useMemo(() => {
		if (!view?.filters || !Array.isArray(view.filters)) return [];

		return view.filters
			.map((viewFilter) => {
				const { field, logic, text, number } = viewFilter;

				switch (logic) {
					case "contains":
					case "equals":
						return { id: field, value: text || number };
					case "in":
						return {
							id: field,
							value: text ? text.split(",") : [],
						};
					case "between":
						return { id: field, value: number || [] };
					default:
						return { id: field, value: text || number };
				}
			})
			.filter(
				(filterItem) =>
					filterItem.value !== undefined && filterItem.value !== null,
			);
	}, [view?.filters, view?.id]);

	// Merge URL filters with view filters
	const mergedFilters = useMemo(() => {
		const { sort, start, size, id, cursor, direction, live, ...filter } = search;

		// Get available column IDs from the config
		const availableColumnIds = new Set(config.columns.map(col => col.id || (col as any).accessorKey));

		const urlFilters = Object.entries(filter)
			.map(([key, value]) => ({
				id: key,
				value,
			}))
			.filter(({ value, id }) => value !== undefined && value !== null && availableColumnIds.has(id));

		const combined = [...convertedViewFilters.filter(f => availableColumnIds.has(f.id))];

		urlFilters.forEach((urlFilter) => {
			const existingIndex = combined.findIndex(
				(f) => f.id === urlFilter.id,
			);
			if (existingIndex >= 0) {
				combined[existingIndex] = urlFilter;
			} else {
				combined.push(urlFilter);
			}
		});

		return combined;
	}, [search, convertedViewFilters, config.columns]);

	// Pagination functions
	const goToPage = (page: number) => {
		const newOffset = (page - 1) * pagination.limit;
		setSearch({ start: newOffset });
	};

	const nextPage = () => {
		if (pagination.hasMore) {
			goToPage(currentPage + 1);
		}
	};

	const previousPage = () => {
		if (currentPage > 1) {
			goToPage(currentPage - 1);
		}
	};

	// Mock functions for DataTableInfinite compatibility
	const mockFetchNextPage = async () => {
		nextPage();
	};

	const mockFetchPreviousPage = async () => {
		previousPage();
	};

	const mockRefetch = async () => {
		await refetch();
	};

	// Define field types for different object types
	const getFieldTypes = (): Record<string, FieldType> => {
		switch (config.type) {
			case "contact":
				return {
					firstName: "text",
					lastName: "text",
					name: "text",
					title: "text",
					summary: "textarea",
					status: "select",
					persona: "select",
					stage: "select",
					email: "array",
					phone: "array",
					company: "company",
					website: "text",
					linkedin: "text",
					facebook: "text",
					twitter: "text",
					instagram: "text",
					tags: "tags",
				};
			case "company":
				return {
					name: "text",
					industry: "text",
					size: "text",
					website: "text",
					email: "array",
					phone: "array",
					description: "textarea",
					tags: "tags",
				};
			case "property":
				return {
					name: "text",
					propertyType: "text",
					description: "textarea",
					price: "number",
					units: "number",
					tags: "tags",
				};
			default:
				return {};
		}
	};

	const getArrayFieldTypes = (): Record<string, "email" | "phone" | "text"> => {
		switch (config.type) {
			case "contact":
			case "company":
				return {
					email: "email",
					phone: "phone",
				};
			default:
				return {};
		}
	};

	const getSelectOptions = (): Record<string, Array<{ label: string; value: string }>> => {
		switch (config.type) {
			case "contact":
				return {
					status: CONTACT_STATUS.map((status) => ({
						label: status.label,
						value: status.value,
					})),
					persona: CONTACT_PERSONA.map((persona) => ({
						label: persona.label,
						value: persona.value,
					})),
					stage: CONTACT_STAGE.map((stage) => ({
						label: stage.label,
						value: stage.value,
					})),
				};
			default:
				return {};
		}
	};

	// Update filter fields with facet data and validate against available columns
	const filterFields = useMemo(() => {
		// Get available column IDs from the config
		const availableColumnIds = new Set(config.columns.map(col => col.id || (col as any).accessorKey));

		return config.filterFields
			.filter((field) => availableColumnIds.has(field.value as string))
			.map((field) => {
				const facetsField = facets?.[field.value as string];
				if (!facetsField || (field.options && field.options.length > 0)) return field;

				const options = facetsField.rows.map((row: any) => ({
					label: `${row.value} (${row.total})`,
					value: row.value,
				}));

				return { ...field, options };
			});
	}, [facets, config.filterFields, config.columns]);

	if (!organizationId) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						Organization not found...
					</p>
				</div>
			</div>
		);
	}

	return (
		<DataTableInfinite
			organizationId={organizationId}
			objectType={config.type}
			columns={config.columns}
			data={flatData}
			totalRows={pagination.total}
			filterRows={pagination.filteredTotal}
			totalRowsFetched={flatData.length}
			hasNextPage={pagination.hasMore}
			defaultColumnFilters={mergedFilters}
			defaultColumnSorting={search.sort ? [search.sort] : undefined}
			defaultRowSelection={search.id ? { [search.id]: true } : undefined}
			defaultColumnVisibility={(() => {
				// If we have a view with column definitions, set visibility based on view settings
				if (view?.columnDefs) {
					const visibility: Record<string, boolean> = {};

					// Map view column fields to actual column IDs and respect their visibility
					view.columnDefs.forEach((colDef) => {
						if (colDef.field) {
							// Handle field name mapping
							let columnId = colDef.field;

							// Special mappings for known field name differences
							if (colDef.field === "company.name") {
								columnId = "company";
							}
							// For address fields, keep the full field name as column ID
							// (e.g., "address.street" stays as "address.street")

							// Use the visible property from columnDef, defaulting to true if not specified
							visibility[columnId] = colDef.visible !== false;
						}
					});

					visibility.select = true;
					return visibility;
				}
				return {};
			})()}
			meta={{
				currentPage,
				totalPages,
				pageSize: pagination.limit,
				total: pagination.total,
			}}
			filterFields={filterFields as any}
			sheetFields={config.sheetFields as any}
			isFetching={isFetching}
			isLoading={isLoading}
			fetchNextPage={mockFetchNextPage}
			fetchPreviousPage={mockFetchPreviousPage}
			refetch={mockRefetch}
			chartData={[]}
			chartDataColumnId="createdAt"
			getRowClassName={(row: any) => ""}
			getRowId={(row: TData) => row.id}
			getFacetedUniqueValues={() => new Map()}
			getFacetedMinMaxValues={() => undefined}
			renderSheetTitle={(props: any) => `${config.type} Details`}
			renderHeader={renderHeader}
			searchParamsParser={config.searchParamsParser}
			primaryColumn={config.primaryColumn}
			view={view}
			onUpdateView={onUpdateView}
			// Pagination mode props
			isPaginationMode={true}
			paginationInfo={{
				currentPage,
				totalPages,
				pageSize: pagination.limit,
				total: pagination.total,
				offset: pagination.offset,
			}}
			onPageChange={goToPage}
			// Context menu handlers
			onEdit={(e, recordId) => {
				console.warn("Edit record:", recordId);
			}}
			onFavorite={(e, recordId) => {
				if (!organizationId) return;
				toggleFavoriteMutation.mutate(
					{
						objectId: recordId,
						objectType: config.type,
						organizationId,
					},
					{
						onSuccess: (result) => {
							toast.success(
								result.added
									? "Added to favorites"
									: "Removed from favorites",
							);
						},
						onError: (error: any) => {
							toast.error(
								"Failed to update favorite: " + error.message,
							);
						},
					},
				);
			}}
			onDelete={(e, recordId) => {
				deleteMutation.mutate(
					{
						id: recordId,
						organizationId,
					},
					{
						onSuccess: () => {
							toast.success("Record deleted successfully");
							refetch();
						},
						onError: (error: any) => {
							toast.error(
								"Failed to delete record: " + error.message,
							);
						},
					},
				);
			}}
			onHide={(e, columnId) => {
				if (!view?.id || !view.columnDefs) return;

				const updatedColumnDefs = view.columnDefs.map((col) => {
					if (col.field === columnId) {
						return { ...col, visible: false };
					}
					return col;
				});

				// Optimistic update
				if (onUpdateView) {
					onUpdateView({
						...view,
						columnDefs: updatedColumnDefs,
					});
				}

				// API call to update the view
				fetch(`/api/object-views/views/${view.id}`, {
					method: "PATCH",
					headers: {
						"Content-Type": "application/json",
					},
					credentials: "include",
					body: JSON.stringify({
						columnDefs: updatedColumnDefs,
					}),
				})
					.then(async (response) => {
						if (!response.ok) {
							const error = await response.json();
							throw new Error(
								error.error || "Failed to update view",
							);
						}
						return response.json();
					})
					.then((updatedView) => {
						if (onUpdateView && updatedView) {
							onUpdateView(updatedView);
						}
					})
					.catch((error) => {
						console.error("🔍 API error:", error);
						toast.error(`Failed to hide column: ${error.message}`);
						// Revert the optimistic update on error
						if (onUpdateView) {
							onUpdateView(view);
						}
					});
			}}
			onEditCell={async (e, cell) => {
				if (!organizationId) return;

				updateFieldMutation.mutate(
					{
						id: cell.rowId,
						field: cell.columnId,
						value: cell.value,
						organizationId,
					},
					{
						onSuccess: () => {
							toast.success("Field updated successfully");
							refetch();
						},
						onError: (error: any) => {
							toast.error(
								"Failed to update field: " + error.message,
							);
						},
					},
				);
			}}
			onClearValue={async (e, cell) => {
				if (!organizationId) return;

				clearFieldMutation.mutate(
					{
						id: cell.rowId,
						field: cell.columnId,
						organizationId,
					},
					{
						onSuccess: () => {
							toast.success("Field cleared successfully");
							refetch();
						},
						onError: (error: any) => {
							toast.error(
								"Failed to clear field: " + error.message,
							);
						},
					},
				);
			}}
			isFavorite={(record) => {
				return favorites.some(
					(fav) =>
						fav.objectId === record.id &&
						fav.objectType === config.type,
				);
			}}
			fieldTypes={getFieldTypes()}
			arrayFieldTypes={getArrayFieldTypes()}
			selectOptions={getSelectOptions()}
			enableInlineEditing={true}
			readonlyColumns={config.readonlyColumns}
		/>
	);
} 