import {
	createParser,
	createSearchParamsCache,
	createSerializer,
	type inferParserType,
	parseAsArrayOf,
	parseAsBoolean,
	parseAsInteger,
	parseAsString,
	parseAsStringLiteral,
	parseAsTimestamp,
} from "nuqs/server";
import {
	ARRAY_DELIMITER,
	parseAsFlexibleTimestamp,
	RANGE_DELIMITER,
	SLIDER_DELIMITER,
	SORT_DELIMITER,
} from "./shared/search-params-factory";
import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

export const parseAsSort = createParser({
	parse(queryValue) {
		const [id, desc] = queryValue.split(SORT_DELIMITER);
		if (!id && !desc) return null;
		return { id, desc: desc === "desc" };
	},
	serialize(value) {
		return `${value.id}.${value.desc ? "desc" : "asc"}`;
	},
});

export const searchParamsParser = {
	// FILTERS
	name: parseAsString,
	firstName: parseAsString,
	lastName: parseAsString,
	email: parseAsString,
	phone: parseAsString,
	status: parseAsArrayOf(
		parseAsStringLiteral(CONTACT_STATUS.map((status) => status.value)),
		ARRAY_DELIMITER,
	),
	persona: parseAsArrayOf(
		parseAsStringLiteral(CONTACT_PERSONA.map((persona) => persona.value)),
		ARRAY_DELIMITER,
	),
	stage: parseAsArrayOf(
		parseAsStringLiteral(CONTACT_STAGE.map((stage) => stage.value)),
		ARRAY_DELIMITER,
	),
	title: parseAsString,
	company: parseAsString,
	tags: parseAsArrayOf(parseAsString, ARRAY_DELIMITER),
	createdAt: parseAsArrayOf(parseAsFlexibleTimestamp, RANGE_DELIMITER),

	// REQUIRED FOR SORTING & PAGINATION
	sort: parseAsSort,
	size: parseAsInteger.withDefault(50),
	start: parseAsInteger.withDefault(0),

	// REQUIRED FOR INFINITE SCROLLING
	direction: parseAsStringLiteral(["prev", "next"]).withDefault("next"),
	cursor: parseAsTimestamp.withDefault(new Date()),
	live: parseAsBoolean.withDefault(false),

	// REQUIRED FOR SELECTION
	id: parseAsString,
};

export const searchParamsCache = createSearchParamsCache(searchParamsParser);
export const searchParamsSerializer = createSerializer(searchParamsParser);

export type SearchParamsType = inferParserType<typeof searchParamsParser>;

export {
	ARRAY_DELIMITER,
	RANGE_DELIMITER,
	SLIDER_DELIMITER,
	SORT_DELIMITER,
};
